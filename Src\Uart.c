#include    "iostm8s003f3.h"
#include    "Define.h"
#include    "hMain.h"
#include    "hEeprom.h"
#include    "hPwm.h"
#include    "hCs1237.h"

void Init_Uart(void)
{
  CLK_PCKENR1 |= CLK_PCKEN1_UART1;      //Usart时钟使能
  nop;
  nop;
  nop;
  nop;
  
	UART1_CR1_M  = 0;                //0------1 Start bit , 8 Data bit    ;     1------1 Start bit , 9 Data bit; 
	UART1_CR1_PCEN = 0;              //0------Parity disable              ;     1------Parity enable
	UART1_CR1_PS   = 0;              //0------Even Parity                 ;     1------Odd Parity
	
	UART1_CR3_STOP = 0;              //0------1 STOP bit                  ;     2------2 STOP bit

#ifdef fMASTER_16M
	//16MHz,9600( DIV = 0x0683 )
	UART1_BRR2     = 0x03;           //USART_DIV = fMaster / baud rate    ;     
	UART1_BRR1     = 0x68;
#endif  

#ifdef fMASTER_8M
	//8MHz,9600( DIV = 0x0341 )
	UART1_BRR2     = 0x01;           //USART_DIV = fMaster / baud rate    ;     
	UART1_BRR1     = 0x34;
#endif  

#ifdef fMASTER_4M
	//4MHz,9600( DIV = 0x01A1 )
	UART1_BRR2     = 0x01;           //USART_DIV = fMaster / baud rate    ;     
	UART1_BRR1     = 0x1A;
#endif  

	UART1_CR2_RIEN = 1;              //0------Receiver interrupt disable  ;     1------Receiver interrupt ensable ( OR , RXNE )
	//USART_CR2_TIEN = 0;              //0------Transmitter interrupt disable  ;     1------Transmitter interrupt ensable ( TXE )

	UART1_CR2_REN = 1;               //0------Receiver disable            ;     1------Receiver ensable	
	UART1_CR2_TEN = 1;               //0------Transmitter disable         ;     1------Transmitter ensable
}

uint SumCheck(uchar *p, uchar Len) 
{ 
  uint     d; 
  
  d = 0;
  while (Len--)
  { 
    d+=*p;
    p++;
  } 
  return (d) ; 
}

#pragma vector=UART1_R_RXNE_vector
__interrupt void UART1_R_RXNE_ISR(void)
{
	uchar c;
	
	if( UART1_SR_OR_LHE || UART1_SR_NF ||UART1_SR_FE )//Overrun error   / Noise  / Framing
		{
			c = UART1_DR;        //清标志
			return;
		}
	
	if( UART1_SR_RXNE )
		{
			c = UART1_DR;
			recv_TimeOut = recv_TimeOutMax;
			FlagCom = 156;                //8.192ms*156 = 1.28S 无通讯
			nop;
			nop;
			nop;
			nop;
			//USART_CR2_TEN = 1;            // TXD使能
			
			switch(recv_state)
			{
				case FSA_INIT:
					            if( c == 0x5a )
					            	{
					            		Com485InBuf[0] = c;
					            		recv_state = FSA_COMMAND;	
					            		recv_index = 1;
					            	}
					            else
					            	{
					            		recv_state = FSA_INIT; 
					            		recv_index = 0;
					            	}
					            	
					            break;
			  
			    case FSA_COMMAND:
		  	            	switch(c)
		  	            	{
		  	            		case ReadNData :
		  	            		case WriteNData:
		  	            			             Com485InBuf[1] = c;
		  	            			             recv_state = FSA_RegisterNum;
		  	            			             recv_index++;
		  	            			             break;
		  	            		default        :
						  	            			     recv_state = FSA_INIT;    
						  	            			     break;
		  	            	}
			  	            break;
	            case FSA_RegisterNum:
		  	            	switch(Com485InBuf[1])
		  	            	{
		  	            		case ReadNData :
		  	            			             Com485InBuf[2] = c;
		  	            			             recv_state = FSA_ByteData;
		  	            			             recv_index++;
		  	            			             break;
		  	            		case WriteNData:
		  	            			             Com485InBuf[2] = c;
		  	            			             recv_state = FSA_ByteNum;
		  	            			             recv_index++;
		  	            			             break;
		  	            		default        :
						  	            			     recv_state = FSA_INIT;    
						  	            			     break;
		  	            	}
			  	            break;	      	
	            case FSA_ByteNum:
	      	            Com485InBuf[ recv_index ] = c;
	      	            recv_index++;
	      	            recv_state = FSA_ByteData;
			  	            break;	  
	           case FSA_ByteData:		
					            Com485InBuf[recv_index] = c;			//编程器读,寄存器数；编程器写,字节总数；
					            recv_index++;
	      	            if( recv_index>ComBufMaxL )
	      	            	{
	      	            		recv_index = 1;
	      	            		recv_state = FSA_INIT;
	      	            		break;
	      	            	}
					            if(Com485InBuf[1]==ReadNData)
					            	{
							            if( recv_index==(Com485InBuf[2]+3) )
							            {
							            	recv_state = FSA_CRCL;
							            	SumDataLength=3+Com485InBuf[2];
							            }					            		
					            	}
					            else
					            	{
							            if( recv_index==(Com485InBuf[3]+4) )
							            {
							            	recv_state = FSA_CRCL;
							            	SumDataLength=4+Com485InBuf[3];
							            }						            		
					            	}

					            break;		  
			  case FSA_CRCL:
			  	            Com485InBuf[recv_index++] = c;			//保存CRC_L
			  	            CrcData.HL[0] = c;
			  	            recv_state = FSA_CRCH;
			  	            break;
			  
			  case FSA_CRCH:
			  	            Com485InBuf[recv_index] = c;			//保存CRC_L
			  	            CrcData.HL[1] = c;
			  	            if(SumCheck(Com485InBuf,SumDataLength)==CrcData.Value)
			  	            	{
			  	            		Flag_Rxd485=0x5a;
			  	            		UART1_CR2_RIEN = 0;  //关接收中断
			  	            	}
			  	            recv_state = FSA_INIT;
			  	            //Flag2.RecvDataing = 0;            //Clear Recving Data Mark
			  	            
			  	            break;
				default:
					            break;
			}
			
		}
}

#pragma vector=UART1_T_TXE_vector
__interrupt void UART1_T_TXE_ISR(void)
{
	if( UART1_SR_TXE )
		{
			if(send_index<send_length)
				{
					UART1_DR = Com485OutBuf[send_index++];
				}
			else
				{
					UART1_CR2_TIEN = 0;                 //关发送中断
					nop;
					nop;
					nop;
					nop;
					nop;
					send_index = 0;
					send_length= 0;
				}
		}
}

//读变量内存，对应数据返回到Com485OutBuf中
void ReadLinUart(uchar Pos,uchar *Com485Out)
{
	uchar i;
	uchar *p;
	
  p = LinUartParam[Pos].Varia;
  
  for(i=0;i< LinUartParam[Pos].Num;i++)
  {
  	*(Com485Out+i) = *p;
  	p++;

  }
}

//写变量内存，Com485InBuf中数据写到对应内存中
void WriteLinUart(uchar Pos,uchar *Com485In)
{
	uchar i;
	uchar *p;
	uchar Num;
	
	//下列地址不能写
	if( (Pos==Pos_Addr_Ch1_DisProData)||(Pos==Pos_Addr_Ch1_OutValue)||(Pos==Pos_Addr_Ch1_aa) )
		{return;}
  
  p = LinUartParam[Pos].Varia;
  Num = LinUartParam[Pos].Num;
  
  for(i=0;i< Num;i++)
  {
  	*p = *(Com485In+i);
  	p++;
  }
  
}

uchar ConvertAddr(uchar adr)
{
	uchar i;
	uchar K;
	
	K = 0;
	for(i=0;i<POS_PARAM_NUM;i++)
	{
		if( adr == PosConvertAddr[ i ].Pos)
			{
				K = i;
				break;
			}
	}
	
	return( K );
}


void LinUart_Exe (void)
{
	uchar Register_FirstAddr;
	uchar Len,i,Register_Num;
	uchar POS;
	
	if(Flag_Rxd485)
		{
			Flag_Rxd485 = 0;
			Len = 0;
			switch(Com485InBuf[1])
			{
				case ReadNData:

					             //Register_Num = Com485InBuf[2];
					             Register_Num = Com485InBuf[2];
					             for(i=0;i<Register_Num;i++)
					             {
					             	Register_FirstAddr = Com485InBuf[3+i];
					             	
					             	//将接收的编程器读取的地址转换成内部数组地址
					             	POS = ConvertAddr( Register_FirstAddr );
					             	
					             	
					             	Com485OutBuf[4+i] = Register_FirstAddr;
					             	Com485OutBuf[4+Register_Num+i] = LinUartParam[ POS ].Num;
					             	
					             	ReadLinUart( POS,&Com485OutBuf[ 4+(Register_Num<<1)+Len] );
					             	
					             	Len += LinUartParam[ POS ].Num;
					             }
					             
					             //计算字节数
					             Len = Len+(Register_Num<<1);
					             Com485OutBuf[0] = Com485InBuf[0];
					             Com485OutBuf[1] = Com485InBuf[1];
					             Com485OutBuf[2] = Register_Num;
					             Com485OutBuf[3] = Len;
				             
					             CrcData.Value = SumCheck(Com485OutBuf,Len+4);
					             Com485OutBuf[Len+4] = CrcData.HL[0];
					             Com485OutBuf[Len+5] = CrcData.HL[1];
					             
					             for(i=0;i<ComBufMaxL;i++)
					             {
					             	Com485InBuf[i] = 0;
					             }
					             send_index = 1;
					             send_length= Len+6;
					             UART1_DR = Com485OutBuf[0];
					             nop;
					             nop;
					             nop;
					             nop;
					             UART1_CR2_TIEN = 1;                     //开发送中断
					             
					             
					             break;
				case WriteNData:
					             Register_FirstAddr = Com485InBuf[2];
					             
					             //将接收的编程器读取的地址转换成内部数组地址
					             POS = ConvertAddr( Register_FirstAddr );
					             
					             if(POS >= POS_PARAM_NUM)                             //超过变量组数
					             	{
					             		Com485OutBuf[3] = 1;                                       //错误码1，变量地址超范围
					             	}
					             else
					             	{
					             		Len = Com485InBuf[3];
					             		
					             		if(Len !=LinUartParam[ POS ].Num)
					             			{
					             				Com485OutBuf[3] = 2;                                   //错误码2，变量字节数错误
					             			}
					             		else
					             			{
					             				WriteLinUart( POS,&Com485InBuf[4]);                    //写RAM变量
					             				WDTCLEAR;
					             				
					             				switch( POS )
					             				{
					             					case Pos_Addr_OutCheck            :
			             						                                         break;

					             					case Pos_Addr_AlarmValue_Open     :
					             						                                     WriteData( Addr_AlarmValue_Open );                               //写eeprom
					             						                                     break;
					             					case Pos_Addr_AlarmValue_Short    :
					             						                                     WriteData( Addr_AlarmValue_Short );                               //写eeprom
					             						                                     break;
					             					case Pos_Addr_AlarmValue_OverRang    :
					             						                                     WriteData( Addr_AlarmValue_OverRang );                               //写eeprom
					             						                                     break;

					             					case Pos_InCaliFun    :
switch(InCaliFun)
{
case 0x12://ch1 mA
case 0x13://ch1  V
  if(  Ch[ 0 ].AdValueA <10000  )
  	{

			Ch[ 0 ].InputCurrent_Zero = Ch[ 0 ].AdValueA;
			WriteData( Addr_Ch1_InputCurrent_Zero );
		}
																									                  
  if( Ch[ 0 ].AdValueA >20000  )	
  	{
  		Ch[ 0 ].InputCurrent = Ch[ 0 ].AdValueA;
  		WriteData( Addr_Ch1_InputCurrent );
  	}	
  	
  Delay(30);
	WDTCLEAR;   
  break;		

case 0x22://ch2 mA
case 0x23://ch2  V
  if( Ch[ 1 ].AdValueA <10000  )
  	{
			Ch[ 1 ].InputCurrent_Zero = Ch[ 1 ].AdValueA;
			WriteData( Addr_Ch2_InputCurrent_Zero );
		}
																									                  
  if( Ch[ 1 ].AdValueA >20000  )	
  	{
  		Ch[ 1 ].InputCurrent = Ch[ 1 ].AdValueA;
  		WriteData( Addr_Ch2_InputCurrent );
  	}
  Delay(30);
	WDTCLEAR; 
  break;	
	default:
		  break;
}
																																			 break;
					             					case Pos_Addr_Ch1_SignalInputType    :
					             						                                     WriteData( Addr_Ch1_SignalInputType );                      //写eeprom
					             						                                     break;
					             					case Pos_Addr_Ch1_SignalOutputType    :
					             						                                     WriteData( Addr_Ch1_SignalOutputType );                     //写eeprom
					             						                                     break;
					             					case Pos_Addr_Ch1_OutCurrentH    :
	             						                                     				switch(Ch[ 0 ].SignalOutputType)
	             						                                     				{
	             						                                     					case OUT_0_5V:
	             						                                     					case OUT_1_5V:
	             						                                     						       Ch[ 0 ].OutCurrentH <<= 2;
	             						                                     						       break;
	             						                                     					case OUT_0_10V:
	             						                                     						       Ch[ 0 ].OutCurrentH <<= 1;
	             						                                     						       break;
	             						                                     				}
			             						                                     		WriteData( Addr_Ch1_OutCurrentH );
			             						                                     		WDTCLEAR;
			             						                                     		Delay(10);
			             						                                     		
					             						                                     break;
					             					case Pos_Addr_Ch1_OutCurrentL    :
		             						                                     				switch(Ch[ 0 ].SignalOutputType)
		             						                                     				{
		             						                                     					case OUT_0_5V:
		             						                                     					case OUT_1_5V:
		             						                                     						       Ch[ 0 ].OutCurrentL <<= 2;
		             						                                     						       break;
		             						                                     					case OUT_0_10V:
		             						                                     						       Ch[ 0 ].OutCurrentL <<= 1;
		             						                                     						       break;
		             						                                     				}
					             						                                     WriteData( Addr_Ch1_OutCurrentL );
					             						                                     WDTCLEAR;
					             						                                     Delay(10);
					             						                                     
					             						                                     break;

					             					case Pos_Addr_Ch2_OutCurrentH    :
	             						                                     				switch(Ch[ 1 ].SignalOutputType)
	             						                                     				{
	             						                                     					case OUT_0_5V:
	             						                                     					case OUT_1_5V:
	             						                                     						       Ch[ 1 ].OutCurrentH <<= 2;
	             						                                     						       break;
	             						                                     					case OUT_0_10V:
	             						                                     						       Ch[ 1 ].OutCurrentH <<= 1;
	             						                                     						       break;
	             						                                     				}
			             						                                     		WriteData( Addr_Ch2_OutCurrentH );
			             						                                     		WDTCLEAR;
			             						                                     		Delay(10);
			             						                                     		
					             						                                     break;
					             					case Pos_Addr_Ch2_OutCurrentL    :
		             						                                     				switch(Ch[ 1 ].SignalOutputType)
		             						                                     				{
		             						                                     					case OUT_0_5V:
		             						                                     					case OUT_1_5V:
		             						                                     						       Ch[ 1 ].OutCurrentL <<= 2;
		             						                                     						       break;
		             						                                     					case OUT_0_10V:
		             						                                     						       Ch[ 1 ].OutCurrentL <<= 1;
		             						                                     						       break;
		             						                                     				}
					             						                                     WriteData( Addr_Ch2_OutCurrentL );
					             						                                     WDTCLEAR;
					             						                                     Delay(10);
					             						                                     
					             						                                     break;

					             					case Pos_Addr_Ch3_OutCurrentH    :
	             						                                     				switch(Ch[ 0 ].SignalOutputType)
	             						                                     				{
	             						                                     					case OUT_0_5V:
	             						                                     					case OUT_1_5V:
	             						                                     						       Ch34[ 0 ].OutCurrentH <<= 2;
	             						                                     						       break;
	             						                                     					case OUT_0_10V:
	             						                                     						       Ch34[ 0 ].OutCurrentH <<= 1;
	             						                                     						       break;
	             						                                     				}
			             						                                     		WriteData( Addr_Ch3_OutCurrentH );
			             						                                     		WDTCLEAR;
			             						                                     		Delay(10);
			             						                                     		
					             						                                     break;
					             					case Pos_Addr_Ch3_OutCurrentL    :
		             						                                     				switch(Ch[ 0 ].SignalOutputType)
		             						                                     				{
		             						                                     					case OUT_0_5V:
		             						                                     					case OUT_1_5V:
		             						                                     						       Ch34[ 0 ].OutCurrentL <<= 2;
		             						                                     						       break;
		             						                                     					case OUT_0_10V:
		             						                                     						       Ch34[ 0 ].OutCurrentL <<= 1;
		             						                                     						       break;
		             						                                     				}
					             						                                     WriteData( Addr_Ch3_OutCurrentL );
					             						                                     WDTCLEAR;
					             						                                     Delay(10);
					             						                                     
					             						                                     break;

					             					case Pos_Addr_Ch4_OutCurrentH    :
	             						                                     				switch(Ch[ 1 ].SignalOutputType)
	             						                                     				{
	             						                                     					case OUT_0_5V:
	             						                                     					case OUT_1_5V:
	             						                                     						       Ch34[ 1 ].OutCurrentH <<= 2;
	             						                                     						       break;
	             						                                     					case OUT_0_10V:
	             						                                     						       Ch34[ 1 ].OutCurrentH <<= 1;
	             						                                     						       break;
	             						                                     				}
			             						                                     		WriteData( Addr_Ch4_OutCurrentH );
			             						                                     		WDTCLEAR;
			             						                                     		Delay(10);
			             						                                     		
					             						                                     break;
					             					case Pos_Addr_Ch4_OutCurrentL    :
		             						                                     				switch(Ch[ 1 ].SignalOutputType)
		             						                                     				{
		             						                                     					case OUT_0_5V:
		             						                                     					case OUT_1_5V:
		             						                                     						       Ch34[ 1 ].OutCurrentL <<= 2;
		             						                                     						       break;
		             						                                     					case OUT_0_10V:
		             						                                     						       Ch34[ 1 ].OutCurrentL <<= 1;
		             						                                     						       break;
		             						                                     				}
					             						                                     WriteData( Addr_Ch4_OutCurrentL );
					             						                                     WDTCLEAR;
					             						                                     Delay(10);
					             						                                     
					             						                                     break;


					             					default    :
					             						                                     
					             						                                     break;
					             					
					             				}

					             				Com485OutBuf[3] = 0;                                   //写成功码0
					             			}
					             	}
					             Com485OutBuf[0] = Com485InBuf[0];
					             Com485OutBuf[1] = Com485InBuf[1];
					             Com485OutBuf[2] = Com485InBuf[2];
					             Len = 0;
					             CrcData.Value = SumCheck(Com485OutBuf,Len+4);
					             Com485OutBuf[Len+4] = CrcData.HL[0];
					             Com485OutBuf[Len+5] = CrcData.HL[1];
					             
					             for(i=0;i<ComBufMaxL;i++)
					             {
					             	Com485InBuf[i] = 0;
					             }
					             send_index = 1;
					             send_length= Len+6;
					             UART1_DR = Com485OutBuf[0];
					             nop;
					             nop;
					             nop;
					             nop;
					             UART1_CR2_TIEN = 1;                     //先写，后开发送中断，因为USART_SR_TXE( 缓存未空 ) 复位时为1
					             break;
				default:
					             
					             break;
			}
			
			UART1_CR2_RIEN = 1;  //恢复接收中断
		}
}



